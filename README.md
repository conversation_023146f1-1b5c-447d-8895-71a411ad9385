# Cattlytics AI

Cattlytics AI is a Python-based application designed to provide advanced analytics and tools for managing cattle-related data. This document explains the structure of the application and provides instructions for setting up and running the app.

## Project Structure

The project is organized as follows:

```
requirements.txt
app/
    main.py
    api/
        router.py
        schemas.py
        swagger.py
        middleware/
            auth_middleware.py
    bots/
        action_agent_bot/
            action_agent.py
        data_query_bot/
            data_query.py
        knowledge_base_bot/
            knowledge_base.py
    supervisor/
        prompts.py
        supervise.py
        tools.py
        
    core/
        config.py
        exception_handler.py
        logger.py
    helpers/
        external_api_service.py
```

### Key Directories and Files

- **`requirements.txt`**: Contains the list of dependencies required for the project.
- **`app/main.py`**: The entry point of the application.
- **`app/api/`**: Contains API-related files such as routers, schemas, and Swagger documentation.
- **`app/api/middleware/`**: Contains middleware for handling tasks such as JWT authorization.
- **`app/bots/`**: Contains bots for various functionalities such as action agents, data queries, and knowledge base management.
- **`app/core/`**: Contains core configuration, exception handling, and logging utilities.
- **`app/helpers/`**: Contains helper services such as external API integrations.

## Setup Instructions

Follow these steps to set up and run the application:

### 1. Create a Virtual Environment

Run the following command to create a virtual environment:

```
python -m venv venv
```

### 2. Activate the Virtual Environment

- On **Windows**:
  ```
  venv\Scripts\activate
  ```
- On **macOS/Linux**:
  ```
  source venv/bin/activate
  ```

### 3. Install Dependencies

Install the required dependencies using the following command:

```
pip install -r requirements.txt
```

### 4. Set Up the `.env` File

Create a `.env` file in the root directory and add the necessary environment variables.

### 5. Run the Application

Start the application by running:

```
python app/main.py
```

The application should now be running and accessible.

## Additional Notes

- Ensure that Python 3.10 or higher is installed on your system.
- For any issues or questions, refer to the documentation or contact the development team.
