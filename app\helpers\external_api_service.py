from fastapi import Request
import httpx
from api.schemas import ExternalAPIRequest
from core.config import settings
from api.schemas import AutoResponseDto


class ExternalAPIService:
    @staticmethod
    async def fetch_data(request: Request, request_dto: ExternalAPIRequest):
        """
        Makes an API call to the given endpoint with the Authorization header.
        """
        headers = {
            "Authorization": f"Bearer {request.state.token}",
            "Source": "Cattlytics-AI-BE",
        }
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.APP_CORE_BE_URL}{request_dto.endpoint}", headers=headers
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            return AutoResponseDto(**response.json())
