#!/usr/bin/env python3
"""Simple test to verify imports work."""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

try:
    print("Testing imports...")
    
    # Test basic imports
    from app.bots.knowledge_base_bot.config import KnowledgeBaseBotConfig
    print("✅ Config import successful")
    
    from app.bots.knowledge_base_bot.knowledge_base import KnowledgeBaseBot
    print("✅ KnowledgeBaseBot import successful")
    
    from app.api.schemas import KnowledgeBaseQueryRequest
    print("✅ API schemas import successful")
    
    # Test configuration creation
    config = KnowledgeBaseBotConfig.from_production()
    print("✅ Configuration creation successful")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
