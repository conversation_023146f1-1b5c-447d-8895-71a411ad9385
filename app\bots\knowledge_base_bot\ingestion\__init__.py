"""
Ingestion module for the Knowledge Base Bot.

This module contains all components responsible for document ingestion:
- Document parsing using Docling
- Document chunking with activity-based filtering
- Text vectorization using OpenAI embeddings
- Vector indexing in Qdrant
"""

from .document_parser import DocumentParser
from .document_chunker import DocumentChunker
from .vectorizer import Vectorizer
from .vector_store import VectorStore

__all__ = [
    "DocumentParser",
    "DocumentChunker", 
    "Vectorizer",
    "VectorStore"
]
