from http import HTTPStatus
from typing import Optional
from starlette.middleware.base import BaseHTTPMiddleware
from jose import jwt
from core.config import settings

# Middleware class for handling JWT authorization
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import HTTPException, Request, Response
from http import HTTPStatus

security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = {
            "/swagger/",
            "/swagger/docs",
            "/swagger/openapi.json",
            "/swagger/heartbeat",
            "/api/knowledge-base/health",  # Health check endpoint
        }

    async def dispatch(self, request: Request, call_next):
        if request.url.path in self.excluded_paths:
            return await call_next(request)

        try:
            credentials: Optional[HTTPAuthorizationCredentials] = await security(
                request
            )

            if not credentials or not credentials.scheme.lower() == "bearer":
                raise HTTPException(
                    status_code=HTTPStatus.UNAUTHORIZED,
                    detail="Invalid or missing authorization credentials",
                )

            token = (
                credentials.credentials
            )  # Get the token string from the Bearer header

            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                audience=settings.VALID_AUDIENCE,
                issuer=settings.VALID_ISSUER,
            )

            email_claim = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
            role_claim = "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"

            user_info = {
                "email": payload.get(email_claim),
                "user_id": payload.get("userId"),
                "ranch_id": payload.get("ranchId"),
                "role": payload.get(role_claim),
            }

            request.state.token = token
            request.state.user = user_info
        except Exception:
            return Response(status_code=HTTPStatus.UNAUTHORIZED, content="Unauthorized")

        return await call_next(request)
