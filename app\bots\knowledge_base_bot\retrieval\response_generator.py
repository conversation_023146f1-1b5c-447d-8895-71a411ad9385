"""
Response Generator for creating contextual responses using retrieved chunks.

This module handles response generation using retrieved document chunks,
conversation history, and system prompts to provide accurate and helpful answers.
"""

from typing import List, Dict, Any, Optional
import asyncio
from dataclasses import dataclass
from loguru import logger

from openai import AsyncOpenAI

from ..config import ResponseGenerationConfig
from .retriever import RetrievedChunk
from .query_processor import ProcessedQuery


@dataclass
class GeneratedResponse:
    """
    Container for generated response with metadata.
    """
    response: str
    sources: List[Dict[str, Any]]
    confidence: float
    tokens_used: int
    model_used: str
    processing_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "response": self.response,
            "sources": self.sources,
            "confidence": self.confidence,
            "tokens_used": self.tokens_used,
            "model_used": self.model_used,
            "processing_time": self.processing_time
        }


class ResponseGenerator:
    """
    Response generator for creating contextual responses using retrieved chunks.
    
    Handles response generation with proper context management, source attribution,
    and conversation history integration for accurate and helpful responses.
    """
    
    def __init__(self, config: ResponseGenerationConfig):
        """
        Initialize the response generator.
        
        Args:
            config: Response generation configuration
        """
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        logger.info(f"Response generator initialized with model: {config.model_name}")
    
    async def generate_response(
        self,
        processed_query: ProcessedQuery,
        retrieved_chunks: List[RetrievedChunk],
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> GeneratedResponse:
        """
        Generate a response using retrieved chunks and conversation context.
        
        Args:
            processed_query: Processed query information
            retrieved_chunks: Retrieved relevant chunks
            conversation_history: Conversation history (up to 10 messages)
            
        Returns:
            GeneratedResponse: Generated response with metadata
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"Generating response for query: {processed_query.original_query}")
            
            if not retrieved_chunks:
                logger.warning("No chunks provided for response generation")
                return self._generate_fallback_response(processed_query, start_time)
            
            # Prepare context from retrieved chunks
            context = self._prepare_context(retrieved_chunks)
            
            # Prepare conversation history
            history_context = self._prepare_conversation_history(conversation_history)
            
            # Generate response using LLM
            response_data = await self._generate_llm_response(
                processed_query, context, history_context
            )
            
            if not response_data:
                return self._generate_fallback_response(processed_query, start_time)
            
            # Prepare source information
            sources = self._prepare_sources(retrieved_chunks)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            return GeneratedResponse(
                response=response_data["response"],
                sources=sources,
                confidence=response_data.get("confidence", 0.7),
                tokens_used=response_data.get("tokens_used", 0),
                model_used=self.config.model_name,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return self._generate_fallback_response(processed_query, start_time)
    
    def _prepare_context(self, chunks: List[RetrievedChunk]) -> str:
        """
        Prepare context string from retrieved chunks.
        
        Args:
            chunks: Retrieved chunks to use as context
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        for i, chunk in enumerate(chunks[:self.config.max_context_chunks]):
            # Format chunk with source information
            source_info = f"[Source {i+1}: {chunk.file_name}]"
            chunk_content = chunk.content.strip()
            
            context_parts.append(f"{source_info}\n{chunk_content}")
        
        return "\n\n".join(context_parts)
    
    def _prepare_conversation_history(
        self,
        conversation_history: Optional[List[Dict[str, str]]]
    ) -> str:
        """
        Prepare conversation history for context.
        
        Args:
            conversation_history: List of conversation exchanges
            
        Returns:
            Formatted conversation history string
        """
        if not conversation_history:
            return ""
        
        # Limit to recent messages (up to 10 as specified)
        recent_history = conversation_history[-self.config.max_history_messages:]
        
        history_parts = []
        for exchange in recent_history:
            role = exchange.get("role", "")
            content = exchange.get("content", "").strip()
            
            if role and content:
                if role == "user":
                    history_parts.append(f"User: {content}")
                elif role == "assistant":
                    history_parts.append(f"Assistant: {content}")
        
        return "\n".join(history_parts) if history_parts else ""
    
    async def _generate_llm_response(
        self,
        processed_query: ProcessedQuery,
        context: str,
        history_context: str
    ) -> Optional[Dict[str, Any]]:
        """
        Generate response using LLM.
        
        Args:
            processed_query: Processed query information
            context: Context from retrieved chunks
            history_context: Conversation history context
            
        Returns:
            Dict with response data or None if failed
        """
        try:
            # Build system prompt
            system_prompt = self._build_system_prompt()
            
            # Build user prompt
            user_prompt = self._build_user_prompt(
                processed_query, context, history_context
            )
            
            # Generate response
            response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                timeout=self.config.timeout_seconds
            )
            
            response_text = response.choices[0].message.content.strip()
            tokens_used = response.usage.total_tokens if response.usage else 0
            
            return {
                "response": response_text,
                "tokens_used": tokens_used,
                "confidence": 0.8  # Could be enhanced with confidence estimation
            }
            
        except Exception as e:
            logger.error(f"Error generating LLM response: {str(e)}")
            return None
    
    def _build_system_prompt(self) -> str:
        """
        Build system prompt for response generation.
        
        Returns:
            System prompt string
        """
        return """You are an expert cattle farming and livestock management assistant. Your role is to provide accurate, helpful, and practical advice based on the provided context from cattle farming documents.

Guidelines:
1. Answer questions using the provided context from cattle farming documents
2. Be specific and practical in your advice
3. If the context doesn't contain enough information, acknowledge this limitation
4. Focus on cattle health, breeding, management, and farming best practices
5. Use clear, professional language appropriate for farmers and livestock managers
6. When referencing information, indicate which source it comes from
7. If asked about something not covered in the context, suggest consulting with veterinarians or agricultural extension services

Always prioritize animal welfare and safety in your recommendations."""
    
    def _build_user_prompt(
        self,
        processed_query: ProcessedQuery,
        context: str,
        history_context: str
    ) -> str:
        """
        Build user prompt with query, context, and history.
        
        Args:
            processed_query: Processed query information
            context: Document context
            history_context: Conversation history
            
        Returns:
            User prompt string
        """
        prompt_parts = []
        
        # Add conversation history if available
        if history_context:
            prompt_parts.append(f"Previous conversation:\n{history_context}\n")
        
        # Add current query
        prompt_parts.append(f"Current question: {processed_query.original_query}")
        
        # Add enhanced query if different
        if processed_query.enhanced_query != processed_query.original_query:
            prompt_parts.append(f"Enhanced query: {processed_query.enhanced_query}")
        
        # Add context from documents
        prompt_parts.append(f"\nRelevant information from cattle farming documents:\n{context}")
        
        # Add instruction
        prompt_parts.append("\nPlease provide a helpful and accurate response based on the above information. If you reference specific information, mention which source it comes from.")
        
        return "\n".join(prompt_parts)
    
    def _prepare_sources(self, chunks: List[RetrievedChunk]) -> List[Dict[str, Any]]:
        """
        Prepare source information from chunks.
        
        Args:
            chunks: Retrieved chunks used in response
            
        Returns:
            List of source information dictionaries
        """
        sources = []
        
        for i, chunk in enumerate(chunks[:self.config.max_context_chunks]):
            source = {
                "source_id": i + 1,
                "file_name": chunk.file_name,
                "chunk_index": chunk.chunk_index,
                "relevance_score": chunk.score,
                "activity_types": chunk.activity_types,
                "word_count": chunk.word_count
            }
            
            # Add rerank score if available
            if chunk.rerank_score is not None:
                source["rerank_score"] = chunk.rerank_score
            
            sources.append(source)
        
        return sources
    
    def _generate_fallback_response(
        self,
        processed_query: ProcessedQuery,
        start_time: float
    ) -> GeneratedResponse:
        """
        Generate fallback response when normal generation fails.
        
        Args:
            processed_query: Processed query information
            start_time: Start time for processing time calculation
            
        Returns:
            Fallback GeneratedResponse
        """
        import time
        
        fallback_text = """I apologize, but I couldn't find specific information in the cattle farming documents to answer your question. 

For the best advice on cattle farming, breeding, and livestock management, I recommend:
1. Consulting with a local veterinarian
2. Contacting your agricultural extension service
3. Reaching out to experienced cattle farmers in your area
4. Checking with livestock management organizations

Is there a different aspect of cattle farming I can help you with?"""
        
        return GeneratedResponse(
            response=fallback_text,
            sources=[],
            confidence=0.1,
            tokens_used=0,
            model_used=self.config.model_name,
            processing_time=time.time() - start_time
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the response generator.
        
        Returns:
            Dict containing health status
        """
        try:
            # Test with a simple generation
            test_response = await self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say 'Health check successful' if you can respond."}
                ],
                temperature=0.1,
                max_tokens=50,
                timeout=10
            )
            
            response_text = test_response.choices[0].message.content.strip()
            
            return {
                "healthy": True,
                "model": self.config.model_name,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "max_context_chunks": self.config.max_context_chunks,
                "max_history_messages": self.config.max_history_messages,
                "test_response": "successful" in response_text.lower()
            }
            
        except Exception as e:
            logger.error(f"Response generator health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e)
            }
