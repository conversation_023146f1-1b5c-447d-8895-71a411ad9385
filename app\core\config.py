from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    SECRET_KEY: str
    ALGORITHM: str
    VALID_ISSUER: str
    VALID_AUDIENCE: str

    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "Cattlytics AI"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Cattlytics AI"

    SWAGGER_USERNAME: str
    SWAGGER_PASSWORD: str

    APP_CORE_BE_URL: str

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


settings = Settings()
