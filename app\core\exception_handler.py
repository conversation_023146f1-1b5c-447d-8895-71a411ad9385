from datetime import datetime
from fastapi import Request, FastAPI
from fastapi.responses import JSONResponse
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR
import traceback
from api.schemas import AutoResponseDto


# Define a global exception handler
def add_global_exception_handler(app: FastAPI):
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        traceback.print_exc()

        # Optional: Log the error or traceback
        response = AutoResponseDto(
            success=False,
            errors=[str(exc)],
            statusCode=HTTP_500_INTERNAL_SERVER_ERROR,
        )

        # Convert to JSON response
        return JSONResponse(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, content=response.dict()
        )
