#!/usr/bin/env python3
"""Minimal test to check if the knowledge base components work."""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

async def test_minimal():
    """Test minimal functionality."""
    try:
        print("Starting minimal test...")
        
        # Test imports one by one
        print("Testing config import...")
        from app.bots.knowledge_base_bot.config import KnowledgeBaseBotConfig
        print("✅ Config imported")
        
        print("Creating config...")
        config = KnowledgeBaseBotConfig.from_production()
        print("✅ Config created")
        
        print("Testing knowledge base bot import...")
        from app.bots.knowledge_base_bot.knowledge_base import KnowledgeBaseBot
        print("✅ KnowledgeBaseBot imported")
        
        print("Creating bot instance...")
        bot = KnowledgeBaseBot(config)
        print("✅ Bot instance created")
        
        print("Testing bot initialization...")
        await bot.initialize()
        print("✅ Bot initialized")
        
        print("Testing health check...")
        health = await bot.get_health_status()
        print(f"✅ Health check completed: {health.get('overall', False)}")
        
        print("Testing simple query...")
        result = await bot.process_query("What is cattle farming?")
        print(f"✅ Query processed: {result.get('success', False)}")
        
        if result.get('success'):
            print(f"Response preview: {result.get('response', '')[:100]}...")
        
        await bot.close()
        print("✅ All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_minimal())
    sys.exit(0 if success else 1)
