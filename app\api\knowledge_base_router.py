"""
Knowledge Base API Router

This module provides REST API endpoints for the Knowledge Base Bot,
including query processing, retrieval, and response generation.
"""

from typing import Dict, Any, List
import asyncio
from datetime import datetime
from loguru import logger

from fastapi import APIRouter, HTTPException, Request, Depends, status
from fastapi.responses import JSONResponse

from api.schemas import (
    AutoResponseDto,
    KnowledgeBaseQueryRequest,
    KnowledgeBaseQueryResponse,
    KnowledgeBaseErrorResponse,
    ConversationMessage,
    SourceInfo,
    QueryProcessingInfo,
    RetrievalStats
)
from bots.knowledge_base_bot.knowledge_base import KnowledgeBaseBot
from bots.knowledge_base_bot.config import KnowledgeBaseBotConfig

# Global knowledge base bot instance
_knowledge_base_bot: KnowledgeBaseBot = None


async def get_knowledge_base_bot() -> KnowledgeBaseBot:
    """
    Dependency to get the knowledge base bot instance.
    
    Returns:
        KnowledgeBaseBot: Initialized knowledge base bot
    """
    global _knowledge_base_bot
    
    if _knowledge_base_bot is None:
        try:
            logger.info("Initializing Knowledge Base Bot...")
            config = KnowledgeBaseBotConfig.from_production()
            _knowledge_base_bot = KnowledgeBaseBot(config)
            
            # Initialize the bot components
            await _knowledge_base_bot.initialize()
            logger.info("Knowledge Base Bot initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Knowledge Base Bot: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize knowledge base: {str(e)}"
            )
    
    return _knowledge_base_bot


# Create router
knowledge_base_router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])


@knowledge_base_router.post(
    "/query",
    response_model=AutoResponseDto[KnowledgeBaseQueryResponse],
    summary="Query Knowledge Base",
    description="Process a user query and return relevant information from the knowledge base"
)
async def query_knowledge_base(
    request: KnowledgeBaseQueryRequest,
    http_request: Request,
    kb_bot: KnowledgeBaseBot = Depends(get_knowledge_base_bot)
) -> AutoResponseDto[KnowledgeBaseQueryResponse]:
    """
    Process a user query against the knowledge base.
    
    This endpoint:
    1. Processes and enhances the user query
    2. Performs vector similarity search
    3. Generates a contextual response
    4. Returns the response with source information
    
    Args:
        request: Query request with user query and optional conversation history
        http_request: FastAPI request object for user context
        kb_bot: Knowledge base bot dependency
        
    Returns:
        AutoResponseDto containing the query response and metadata
    """
    response_dto = AutoResponseDto[KnowledgeBaseQueryResponse]()
    
    try:
        logger.info(f"Processing knowledge base query: {request.query[:100]}...")
        
        # Extract user information from request state (set by auth middleware)
        user_info = getattr(http_request.state, 'user', {})
        user_id = user_info.get('user_id', 'unknown')
        ranch_id = user_info.get('ranch_id', 'unknown')
        
        logger.info(f"Query from user {user_id}, ranch {ranch_id}")
        
        # Convert conversation history to the format expected by the bot
        conversation_history = None
        if request.conversation_history:
            conversation_history = [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp.isoformat() if msg.timestamp else None
                }
                for msg in request.conversation_history
            ]
        
        # Process the query using the knowledge base bot
        result = await kb_bot.process_query(
            user_query=request.query,
            conversation_history=conversation_history
        )
        
        if not result.get("success", False):
            error_msg = result.get("error", "Unknown error occurred")
            logger.error(f"Knowledge base query failed: {error_msg}")
            
            response_dto.add_error(f"Query processing failed: {error_msg}")
            response_dto.set_status_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
            return response_dto
        
        # Convert the result to our response schema
        sources = []
        if request.include_sources and result.get("sources"):
            for source in result["sources"]:
                source_info = SourceInfo(
                    file_name=source.get("file_name", ""),
                    file_path=source.get("file_path", ""),
                    chunk_index=source.get("chunk_index", 0),
                    heading_text=source.get("heading_text"),
                    heading_level=source.get("heading_level"),
                    score=source.get("score", 0.0),
                    word_count=source.get("word_count", 0)
                )
                sources.append(source_info)
        
        # Convert query processing info
        query_info_data = result.get("query_info", {})
        query_info = QueryProcessingInfo(
            original_query=query_info_data.get("original_query", request.query),
            enhanced_query=query_info_data.get("enhanced_query", request.query),
            rephrased_queries=query_info_data.get("rephrased_queries", []),
            keywords=query_info_data.get("keywords", []),
            intent=query_info_data.get("intent", "general"),
            confidence=query_info_data.get("confidence", 0.5),
            activity_filters=query_info_data.get("activity_filters", [])
        )
        
        # Convert retrieval stats
        retrieval_stats_data = result.get("retrieval_stats", {})
        retrieval_stats = RetrievalStats(
            total_chunks_found=retrieval_stats_data.get("total_chunks_found", 0),
            chunks_returned=retrieval_stats_data.get("chunks_returned", 0),
            search_time_ms=retrieval_stats_data.get("search_time_ms", 0.0),
            average_score=retrieval_stats_data.get("average_score", 0.0),
            score_threshold=retrieval_stats_data.get("score_threshold", 0.0)
        )
        
        # Create the response
        kb_response = KnowledgeBaseQueryResponse(
            response=result.get("response", ""),
            sources=sources,
            confidence=result.get("confidence", 0.5),
            tokens_used=result.get("tokens_used", 0),
            processing_time=result.get("processing_time", 0.0),
            model_used=result.get("model_used", "gpt-4o"),
            query_info=query_info,
            retrieval_stats=retrieval_stats
        )
        
        response_dto.result = kb_response
        response_dto.add_message("Query processed successfully")
        
        logger.info(f"Query processed successfully in {result.get('processing_time', 0):.2f}s")
        return response_dto
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
        
    except Exception as e:
        logger.error(f"Unexpected error processing query: {str(e)}")
        response_dto.add_error(f"Internal server error: {str(e)}")
        response_dto.set_status_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
        return response_dto


@knowledge_base_router.get(
    "/health",
    summary="Knowledge Base Health Check",
    description="Check the health status of the knowledge base components"
)
async def health_check(
    kb_bot: KnowledgeBaseBot = Depends(get_knowledge_base_bot)
) -> AutoResponseDto[Dict[str, Any]]:
    """
    Check the health status of knowledge base components.
    
    Returns:
        AutoResponseDto containing health status information
    """
    response_dto = AutoResponseDto[Dict[str, Any]]()
    
    try:
        # Get health status from the knowledge base bot
        health_status = await kb_bot.get_health_status()
        
        response_dto.result = health_status
        response_dto.add_message("Health check completed")
        
        return response_dto
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        response_dto.add_error(f"Health check failed: {str(e)}")
        response_dto.set_status_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
        return response_dto


@knowledge_base_router.get(
    "/stats",
    summary="Knowledge Base Statistics",
    description="Get statistics about the knowledge base content and performance"
)
async def get_stats(
    kb_bot: KnowledgeBaseBot = Depends(get_knowledge_base_bot)
) -> AutoResponseDto[Dict[str, Any]]:
    """
    Get knowledge base statistics.
    
    Returns:
        AutoResponseDto containing knowledge base statistics
    """
    response_dto = AutoResponseDto[Dict[str, Any]]()
    
    try:
        # Get statistics from the knowledge base bot
        stats = await kb_bot.get_statistics()
        
        response_dto.result = stats
        response_dto.add_message("Statistics retrieved successfully")
        
        return response_dto
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {str(e)}")
        response_dto.add_error(f"Failed to get statistics: {str(e)}")
        response_dto.set_status_code(status.HTTP_500_INTERNAL_SERVER_ERROR)
        return response_dto
