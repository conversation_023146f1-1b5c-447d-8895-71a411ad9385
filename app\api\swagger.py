# Import necessary modules for API routing and security
from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPBasicCredentials
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from core.config import settings
import secrets

# Create a router for Swagger-related endpoints
swagger_router = APIRouter(include_in_schema=False)

# Define basic HTTP security for Swagger authentication
security = HTTPBasic()


# Function to validate user credentials for accessing Swagger UI
def validate_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    # Compare provided username and password with environment variables
    correct_username = secrets.compare_digest(
        credentials.username, settings.SWAGGER_USERNAME
    )
    correct_password = secrets.compare_digest(
        credentials.password, settings.SWAGGER_PASSWORD
    )
    # Raise an exception if credentials are invalid
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


# Endpoint to return the OpenAPI schema with authentication
@swagger_router.get("/openapi.json")
def custom_openapi(credentials: HTTPBasicCredentials = Depends(validate_credentials)):
    from main import app

    """
    Protected OpenAPI schema with JWT Bearer support.
    """
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Inject JWT Bearer security schema
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }

    # Optional: apply security globally to all endpoints
    openapi_schema["security"] = [{"BearerAuth": []}]

    return openapi_schema


# Endpoint to serve the Swagger UI with authentication
@swagger_router.get("/")
def custom_docs(credentials: HTTPBasicCredentials = Depends(validate_credentials)):
    return get_swagger_ui_html(
        openapi_url="/swagger/openapi.json", title=settings.PROJECT_NAME
    )


# Endpoint to check the health of the application
@swagger_router.head("/heartbeat")
def health_check():
    return {"status": "healthy"}
