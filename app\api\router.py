from fastapi import APIRouter, Depends, Request

# Import the text_analysis router
from api.schemas import ExternalAPIRequest
from helpers.external_api_service import ExternalAPIService

api_router = APIRouter()


# Include further routes
@api_router.get("/request-be")
async def fetch_data_from_endpoint(
    request: Request, requestDto: ExternalAPIRequest = Depends()
):
    """
    Fetch data from the given endpoint using the Authorization token from the request.
    """
    return await ExternalAPIService.fetch_data(request, requestDto)
