#!/usr/bin/env python3
"""
Test script for the complete retrieval pipeline.

This script tests the end-to-end retrieval process:
1. Initialize the knowledge base bot
2. Test query processing and enhancement
3. Test vector search and retrieval
4. Test response generation
5. Test the complete API endpoint

Usage:
    python test_retrieval_pipeline.py
"""

import asyncio
import json
import time
from typing import List, Dict, Any
from loguru import logger
import httpx

# Configure logging
logger.add("test_retrieval_pipeline.log", rotation="10 MB", level="INFO")


async def test_knowledge_base_bot():
    """Test the knowledge base bot directly."""
    try:
        logger.info("=== Testing Knowledge Base Bot Directly ===")
        
        # Import and initialize
        from app.bots.knowledge_base_bot.knowledge_base import KnowledgeBaseBot
        from app.bots.knowledge_base_bot.config import KnowledgeBaseBotConfig
        
        # Initialize configuration
        config = KnowledgeBaseBotConfig.from_production()
        logger.info("Configuration loaded successfully")
        
        # Initialize bot
        bot = KnowledgeBaseBot(config)
        await bot.initialize()
        logger.info("Knowledge Base Bot initialized successfully")
        
        # Test health check
        logger.info("Testing health check...")
        health_status = await bot.get_health_status()
        logger.info(f"Health status: {json.dumps(health_status, indent=2)}")
        
        if not health_status.get("overall", False):
            logger.error("Health check failed - some components are not healthy")
            return False
        
        # Test statistics
        logger.info("Testing statistics...")
        stats = await bot.get_statistics()
        logger.info(f"Statistics: {json.dumps(stats, indent=2)}")
        
        # Test queries
        test_queries = [
            "How do I care for newborn calves?",
            "What are the signs of mastitis in dairy cows?",
            "How often should I vaccinate my cattle?",
            "What is the best feed for pregnant cows?",
            "How do I manage pasture rotation?"
        ]
        
        logger.info("Testing queries...")
        for i, query in enumerate(test_queries, 1):
            logger.info(f"Testing query {i}: {query}")
            
            start_time = time.time()
            result = await bot.process_query(query)
            processing_time = time.time() - start_time
            
            if result.get("success", False):
                logger.info(f"Query {i} successful:")
                logger.info(f"  - Response length: {len(result.get('response', ''))}")
                logger.info(f"  - Sources: {len(result.get('sources', []))}")
                logger.info(f"  - Confidence: {result.get('confidence', 0):.2f}")
                logger.info(f"  - Processing time: {processing_time:.2f}s")
                logger.info(f"  - Response preview: {result.get('response', '')[:200]}...")
            else:
                logger.error(f"Query {i} failed: {result.get('error', 'Unknown error')}")
                return False
        
        # Test conversation history
        logger.info("Testing conversation history...")
        conversation_history = [
            {"role": "user", "content": "What is mastitis?"},
            {"role": "assistant", "content": "Mastitis is an inflammation of the mammary gland in dairy cows..."}
        ]
        
        result = await bot.process_query(
            "How do I prevent it?", 
            conversation_history=conversation_history
        )
        
        if result.get("success", False):
            logger.info("Conversation history test successful")
            logger.info(f"Response: {result.get('response', '')[:200]}...")
        else:
            logger.error(f"Conversation history test failed: {result.get('error', 'Unknown error')}")
            return False
        
        # Clean up
        await bot.close()
        logger.info("Knowledge Base Bot test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Knowledge Base Bot test failed: {str(e)}")
        return False


async def test_api_endpoint():
    """Test the API endpoint."""
    try:
        logger.info("=== Testing API Endpoint ===")
        
        # Test data
        test_requests = [
            {
                "query": "How do I care for newborn calves?",
                "include_sources": True,
                "max_chunks": 5
            },
            {
                "query": "What are the signs of mastitis?",
                "conversation_history": [
                    {
                        "role": "user",
                        "content": "Tell me about dairy cow diseases"
                    },
                    {
                        "role": "assistant", 
                        "content": "There are several common diseases that affect dairy cows..."
                    }
                ],
                "include_sources": True,
                "max_chunks": 3
            }
        ]
        
        # Note: This would require a running server and valid JWT token
        # For now, we'll just validate the request structure
        logger.info("API endpoint test structure validation:")
        
        for i, request_data in enumerate(test_requests, 1):
            logger.info(f"Test request {i}: {json.dumps(request_data, indent=2)}")
            
            # Validate required fields
            if "query" not in request_data:
                logger.error(f"Request {i} missing required 'query' field")
                return False
            
            if len(request_data["query"]) == 0:
                logger.error(f"Request {i} has empty query")
                return False
            
            logger.info(f"Request {i} structure is valid")
        
        logger.info("API endpoint structure validation completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"API endpoint test failed: {str(e)}")
        return False


async def test_component_integration():
    """Test individual component integration."""
    try:
        logger.info("=== Testing Component Integration ===")
        
        from app.bots.knowledge_base_bot.config import KnowledgeBaseBotConfig
        from app.bots.knowledge_base_bot.retrieval.query_processor import QueryProcessor
        from app.bots.knowledge_base_bot.ingestion.vectorizer import Vectorizer
        from app.bots.knowledge_base_bot.ingestion.vector_store import VectorStore
        from app.bots.knowledge_base_bot.retrieval.retriever import Retriever
        from app.bots.knowledge_base_bot.retrieval.response_generator import ResponseGenerator
        
        # Initialize configuration
        config = KnowledgeBaseBotConfig.from_production()
        
        # Test query processor
        logger.info("Testing Query Processor...")
        query_processor = QueryProcessor(config.query_processing)
        processed_query = await query_processor.process_query("How do I care for calves?")
        
        logger.info(f"Query processing result:")
        logger.info(f"  - Original: {processed_query.original_query}")
        logger.info(f"  - Enhanced: {processed_query.enhanced_query}")
        logger.info(f"  - Rephrased: {processed_query.rephrased_queries}")
        logger.info(f"  - Keywords: {processed_query.keywords}")
        logger.info(f"  - Intent: {processed_query.intent}")
        logger.info(f"  - Confidence: {processed_query.confidence}")
        
        # Test vectorizer
        logger.info("Testing Vectorizer...")
        vectorizer = Vectorizer(config.vectorization)
        query_vector = await vectorizer.vectorize_query("How do I care for calves?")
        
        if query_vector and len(query_vector) > 0:
            logger.info(f"Vectorization successful - vector dimension: {len(query_vector)}")
        else:
            logger.error("Vectorization failed")
            return False
        
        # Test vector store
        logger.info("Testing Vector Store...")
        vector_store = VectorStore(config.vector_store)
        await vector_store.initialize_collection()
        
        # Test search (this will work if data was ingested)
        search_results = await vector_store.search(
            query_vector=query_vector,
            top_k=3,
            score_threshold=0.1
        )
        
        logger.info(f"Vector search returned {len(search_results)} results")
        for i, result in enumerate(search_results[:2]):  # Show first 2 results
            logger.info(f"Result {i+1}:")
            logger.info(f"  - Score: {result.get('score', 0):.3f}")
            logger.info(f"  - File: {result.get('file_name', 'unknown')}")
            logger.info(f"  - Content preview: {result.get('content', '')[:100]}...")
        
        # Test retriever
        logger.info("Testing Retriever...")
        retriever = Retriever(vector_store, config.retrieval)
        retriever.set_vectorizer(vectorizer)
        
        retrieved_chunks = await retriever.retrieve(processed_query)
        logger.info(f"Retriever returned {len(retrieved_chunks)} chunks")
        
        # Test response generator
        logger.info("Testing Response Generator...")
        response_generator = ResponseGenerator(config.response_generation)
        
        if retrieved_chunks:
            response = await response_generator.generate_response(
                processed_query, retrieved_chunks
            )
            
            logger.info(f"Response generation result:")
            logger.info(f"  - Response length: {len(response.response)}")
            logger.info(f"  - Sources: {len(response.sources)}")
            logger.info(f"  - Confidence: {response.confidence}")
            logger.info(f"  - Tokens used: {response.tokens_used}")
            logger.info(f"  - Model: {response.model_used}")
            logger.info(f"  - Response preview: {response.response[:200]}...")
        else:
            logger.warning("No chunks retrieved - skipping response generation test")
        
        # Clean up
        await vector_store.close()
        
        logger.info("Component integration test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Component integration test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    logger.info("Starting retrieval pipeline tests...")
    
    tests = [
        ("Component Integration", test_component_integration),
        ("Knowledge Base Bot", test_knowledge_base_bot),
        ("API Endpoint", test_api_endpoint)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Retrieval pipeline is working correctly.")
        return True
    else:
        logger.error("⚠️  Some tests failed. Please check the logs for details.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
