"""
Knowledge Base Bot - Main orchestrator for document ingestion and retrieval.

This module provides the main interface for the knowledge base bot, coordinating
between ingestion and retrieval components to provide intelligent document-based
question answering capabilities.
"""

from typing import List, Dict, Any, Optional, Union
import asyncio
from pathlib import Path
from loguru import logger

from .ingestion.document_parser import DocumentParser
from .ingestion.document_chunker import DocumentChunker
from .ingestion.vectorizer import Vectorizer
from .ingestion.vector_store import VectorStore
from .retrieval.query_processor import QueryProcessor
from .retrieval.retriever import Retriever
from .retrieval.response_generator import ResponseGenerator
from .config import KnowledgeBaseConfig
from .utils import setup_logging, validate_file_for_processing, Timer


class KnowledgeBaseBot:
    """
    Main knowledge base bot class that orchestrates document ingestion and retrieval.

    This class provides a high-level interface for:
    - Ingesting documents (parsing, chunking, vectorizing, indexing)
    - Processing user queries and generating responses
    - Managing conversation context
    """

    def __init__(self, config: KnowledgeBaseConfig):
        """
        Initialize the knowledge base bot with configuration.

        Args:
            config: Configuration object containing all necessary settings
        """
        self.config = config
        logger.info("Initializing Knowledge Base Bot")

        # Initialize ingestion components
        self.document_parser = DocumentParser(config.parsing)
        self.document_chunker = DocumentChunker(config.chunking)
        self.vectorizer = Vectorizer(config.vectorization)
        self.vector_store = VectorStore(config.vector_store)

        # Initialize retrieval components
        self.query_processor = QueryProcessor(config.query_processing)
        self.retriever = Retriever(self.vector_store, config.retrieval)
        self.response_generator = ResponseGenerator(config.response_generation)

        # Set vectorizer for retriever
        self.retriever.set_vectorizer(self.vectorizer)

        logger.info("Knowledge Base Bot initialized successfully")

    async def ingest_document(self, document_path: Union[str, Path], metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Ingest a single document into the knowledge base.

        Args:
            document_path: Path to the document to ingest
            metadata: Optional metadata to associate with the document

        Returns:
            bool: True if ingestion was successful, False otherwise
        """
        try:
            logger.info(f"Starting ingestion of document: {document_path}")

            # Validate file before processing
            validation_result = validate_file_for_processing(document_path)
            if not validation_result["valid"]:
                logger.error(f"File validation failed for {document_path}: {validation_result['errors']}")
                return False

            with Timer(f"Document ingestion for {Path(document_path).name}"):
                # Step 1: Parse document
                parsed_content = await self.document_parser.parse(document_path)
                if not parsed_content:
                    logger.error(f"Failed to parse document: {document_path}")
                    return False

                # Step 2: Chunk document
                chunks = await self.document_chunker.chunk(parsed_content, metadata)
                if not chunks:
                    logger.error(f"Failed to chunk document: {document_path}")
                    return False

                # Step 3: Vectorize chunks
                vectorized_chunks = await self.vectorizer.vectorize_chunks(chunks)
                if not vectorized_chunks:
                    logger.error(f"Failed to vectorize chunks for document: {document_path}")
                    return False

                # Step 4: Index in vector store
                success = await self.vector_store.index_chunks(vectorized_chunks)
                if not success:
                    logger.error(f"Failed to index chunks for document: {document_path}")
                    return False

            logger.info(f"Successfully ingested document: {document_path} ({len(chunks)} chunks)")
            return True

        except Exception as e:
            logger.error(f"Error ingesting document {document_path}: {str(e)}")
            return False

    async def ingest_documents(self, document_paths: List[Union[str, Path]], metadata_list: Optional[List[Dict[str, Any]]] = None) -> Dict[str, bool]:
        """
        Ingest multiple documents into the knowledge base.

        Args:
            document_paths: List of paths to documents to ingest
            metadata_list: Optional list of metadata dictionaries for each document

        Returns:
            Dict[str, bool]: Dictionary mapping document paths to ingestion success status
        """
        if metadata_list and len(metadata_list) != len(document_paths):
            raise ValueError("metadata_list length must match document_paths length")

        results = {}
        tasks = []

        for i, doc_path in enumerate(document_paths):
            metadata = metadata_list[i] if metadata_list else None
            task = self.ingest_document(doc_path, metadata)
            tasks.append((doc_path, task))

        # Execute ingestion tasks concurrently
        for doc_path, task in tasks:
            try:
                result = await task
                results[doc_path] = result
            except Exception as e:
                logger.error(f"Error in concurrent ingestion for {doc_path}: {str(e)}")
                results[doc_path] = False

        return results

    async def process_query(self, user_query: str, conversation_history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        Process a user query and generate a response (alias for query method).

        Args:
            user_query: The user's question or query
            conversation_history: Optional conversation history

        Returns:
            Dict containing response and metadata
        """
        return await self.query(user_query, conversation_history)

    async def query(self, user_query: str, conversation_history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        Process a user query and generate a response.

        Args:
            user_query: The user's question or query
            conversation_history: Optional conversation history for context

        Returns:
            Dict containing the response and metadata
        """
        try:
            logger.info(f"Processing query: {user_query}")

            # Step 1: Process and enhance the query
            processed_query = await self.query_processor.process_query(
                user_query, conversation_history
            )

            # Step 2: Retrieve relevant chunks
            retrieved_chunks = await self.retriever.retrieve(
                processed_query, conversation_history
            )

            # Step 3: Generate response
            response = await self.response_generator.generate_response(
                processed_query, retrieved_chunks, conversation_history
            )

            logger.info("Query processed successfully")
            return {
                "response": response.response,
                "sources": response.sources,
                "confidence": response.confidence,
                "tokens_used": response.tokens_used,
                "processing_time": response.processing_time,
                "success": True,
                "query_info": processed_query.to_dict(),
                "retrieval_stats": self.retriever.get_retrieval_stats(retrieved_chunks)
            }

        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                "response": "I apologize, but I encountered an error while processing your query. Please try again.",
                "error": str(e),
                "success": False
            }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on all components.

        Returns:
            Dict containing health status of all components
        """
        health_status = {
            "overall": True,
            "components": {}
        }

        try:
            # Check vector store connection
            vector_store_health = await self.vector_store.health_check()
            health_status["components"]["vector_store"] = vector_store_health

            # Check vectorizer
            vectorizer_health = await self.vectorizer.health_check()
            health_status["components"]["vectorizer"] = vectorizer_health

            # Check query processor
            query_processor_health = await self.query_processor.health_check()
            health_status["components"]["query_processor"] = query_processor_health

            # Check response generator
            response_generator_health = await self.response_generator.health_check()
            health_status["components"]["response_generator"] = response_generator_health

            # Update overall status
            health_status["overall"] = all(
                status.get("healthy", False)
                for status in health_status["components"].values()
            )

        except Exception as e:
            logger.error(f"Error during health check: {str(e)}")
            health_status["overall"] = False
            health_status["error"] = str(e)

        return health_status

    async def delete_document(self, file_path: Union[str, Path]) -> bool:
        """
        Delete all chunks associated with a document from the vector store.

        Args:
            file_path: Path to the document to delete

        Returns:
            bool: True if deletion was successful
        """
        try:
            success = await self.vector_store.delete_by_file(str(file_path))
            if success:
                logger.info(f"Successfully deleted document: {file_path}")
            else:
                logger.error(f"Failed to delete document: {file_path}")
            return success

        except Exception as e:
            logger.error(f"Error deleting document {file_path}: {str(e)}")
            return False

    async def get_collection_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the vector store collection.

        Returns:
            Dict with collection information or None if error
        """
        try:
            return await self.vector_store.get_collection_info()
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            return None

    async def initialize(self):
        """
        Initialize the knowledge base bot components.

        This method ensures all components are properly initialized and ready for use.
        """
        try:
            logger.info("Initializing Knowledge Base Bot components...")

            # Initialize vector store collection
            await self.vector_store.initialize_collection()
            logger.info("Vector store initialized")

            # Verify all components are healthy
            health_status = await self.get_health_status()
            if not health_status.get("overall", False):
                logger.warning("Some components failed health check")
                logger.warning(f"Health status: {health_status}")
            else:
                logger.info("All components initialized and healthy")

        except Exception as e:
            logger.error(f"Error initializing Knowledge Base Bot: {str(e)}")
            raise

    async def get_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive health status of all components.

        Returns:
            Dict containing health status of all components
        """
        health_status = {
            "overall": False,
            "timestamp": asyncio.get_event_loop().time(),
            "components": {}
        }

        try:
            # Check vector store connection
            vector_store_health = await self.vector_store.health_check()
            health_status["components"]["vector_store"] = vector_store_health

            # Check vectorizer
            vectorizer_health = await self.vectorizer.health_check()
            health_status["components"]["vectorizer"] = vectorizer_health

            # Check query processor
            query_processor_health = await self.query_processor.health_check()
            health_status["components"]["query_processor"] = query_processor_health

            # Check response generator
            response_generator_health = await self.response_generator.health_check()
            health_status["components"]["response_generator"] = response_generator_health

            # Update overall status
            health_status["overall"] = all(
                status.get("healthy", False)
                for status in health_status["components"].values()
            )

        except Exception as e:
            logger.error(f"Error during health check: {str(e)}")
            health_status["overall"] = False
            health_status["error"] = str(e)

        return health_status

    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about the knowledge base.

        Returns:
            Dict containing knowledge base statistics
        """
        try:
            stats = {
                "timestamp": asyncio.get_event_loop().time(),
                "collection_info": None,
                "component_stats": {}
            }

            # Get collection information
            collection_info = await self.get_collection_info()
            if collection_info:
                stats["collection_info"] = collection_info

            # Get component-specific statistics
            try:
                # Vector store stats
                vector_store_health = await self.vector_store.health_check()
                stats["component_stats"]["vector_store"] = vector_store_health

                # Add more component stats as needed
                stats["component_stats"]["config"] = {
                    "vectorization_model": self.config.vectorization.model_name,
                    "response_model": self.config.response_generation.model_name,
                    "max_context_chunks": self.config.response_generation.max_context_chunks,
                    "max_conversation_history": self.config.response_generation.max_conversation_history
                }

            except Exception as e:
                logger.warning(f"Error getting component stats: {str(e)}")
                stats["component_stats"]["error"] = str(e)

            return stats

        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return {
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }

    async def close(self):
        """Close all connections and cleanup resources."""
        try:
            await self.vector_store.close()
            logger.info("Knowledge Base Bot closed successfully")
        except Exception as e:
            logger.error(f"Error closing Knowledge Base Bot: {str(e)}")

    def __del__(self):
        """Cleanup on deletion."""
        try:
            asyncio.create_task(self.close())
        except Exception:
            pass  # Ignore errors during cleanup