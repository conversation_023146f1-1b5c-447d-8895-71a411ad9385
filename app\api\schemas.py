from typing import Generic, List, Optional, TypeVar, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from fastapi import status

T = TypeVar("T")


class AutoResponseDto(BaseModel, Generic[T]):
    success: bool = True
    result: Optional[T] = None
    message: Optional[str] = None
    errors: List[str] = []
    warnings: List[str] = []
    statusCode: int = status.HTTP_200_OK
    exception: Optional[str] = None
    currentUtcDateTime: str = Field(default_factory=lambda: str(datetime.utcnow()))
    frontendBatchId: Optional[str] = None

    def add_error(self, content: str):
        if content:
            self.errors.append(content)
            self.success = False
            self.statusCode = status.HTTP_400_BAD_REQUEST

    def add_warning(self, content: str):
        if content:
            self.warnings.append(content)

    def add_message(self, content: str):
        if content:
            self.message = content

    def set_status_code(self, code: int):
        self.statusCode = code


class ExternalAPIRequest(BaseModel):
    endpoint: str


# Knowledge Base Schemas
class ConversationMessage(BaseModel):
    """Schema for conversation history messages."""
    role: str = Field(..., description="Message role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: Optional[datetime] = Field(default=None, description="Message timestamp")


class KnowledgeBaseQueryRequest(BaseModel):
    """Schema for knowledge base query requests."""
    query: str = Field(..., min_length=1, max_length=1000, description="User query")
    conversation_history: Optional[List[ConversationMessage]] = Field(
        default=None,
        max_items=10,
        description="Previous conversation messages (up to 10)"
    )
    include_sources: bool = Field(default=True, description="Include source information in response")
    max_chunks: Optional[int] = Field(default=5, ge=1, le=10, description="Maximum chunks to retrieve")


class SourceInfo(BaseModel):
    """Schema for source information."""
    file_name: str = Field(..., description="Source file name")
    file_path: str = Field(..., description="Source file path")
    chunk_index: int = Field(..., description="Chunk index in document")
    heading_text: Optional[str] = Field(default=None, description="Section heading")
    heading_level: Optional[int] = Field(default=None, description="Heading level")
    score: float = Field(..., description="Relevance score")
    word_count: int = Field(..., description="Word count of chunk")


class QueryProcessingInfo(BaseModel):
    """Schema for query processing information."""
    original_query: str = Field(..., description="Original user query")
    enhanced_query: str = Field(..., description="Enhanced/expanded query")
    rephrased_queries: List[str] = Field(default=[], description="Alternative query phrasings")
    keywords: List[str] = Field(default=[], description="Extracted keywords")
    intent: str = Field(..., description="Detected query intent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Processing confidence")
    activity_filters: List[str] = Field(default=[], description="Applied activity filters")


class RetrievalStats(BaseModel):
    """Schema for retrieval statistics."""
    total_chunks_found: int = Field(..., description="Total chunks found")
    chunks_returned: int = Field(..., description="Chunks returned after filtering")
    search_time_ms: float = Field(..., description="Search time in milliseconds")
    average_score: float = Field(..., description="Average relevance score")
    score_threshold: float = Field(..., description="Score threshold used")


class KnowledgeBaseQueryResponse(BaseModel):
    """Schema for knowledge base query responses."""
    response: str = Field(..., description="Generated response")
    sources: List[SourceInfo] = Field(default=[], description="Source information")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Response confidence")
    tokens_used: int = Field(..., description="Tokens used for generation")
    processing_time: float = Field(..., description="Total processing time in seconds")
    model_used: str = Field(..., description="Model used for response generation")
    query_info: QueryProcessingInfo = Field(..., description="Query processing details")
    retrieval_stats: RetrievalStats = Field(..., description="Retrieval statistics")


class KnowledgeBaseErrorResponse(BaseModel):
    """Schema for knowledge base error responses."""
    error: str = Field(..., description="Error message")
    error_type: str = Field(..., description="Error type")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
