from typing import Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field
from datetime import datetime
from fastapi import status

T = TypeVar("T")


class AutoResponseDto(BaseModel, Generic[T]):
    success: bool = True
    result: Optional[T] = None
    message: Optional[str] = None
    errors: List[str] = []
    warnings: List[str] = []
    statusCode: int = status.HTTP_200_OK
    exception: Optional[str] = None
    currentUtcDateTime: str = Field(default_factory=lambda: str(datetime.utcnow()))
    frontendBatchId: Optional[str] = None

    def add_error(self, content: str):
        if content:
            self.errors.append(content)
            self.success = False
            self.statusCode = status.HTTP_400_BAD_REQUEST

    def add_warning(self, content: str):
        if content:
            self.warnings.append(content)

    def add_message(self, content: str):
        if content:
            self.message = content

    def set_status_code(self, code: int):
        self.statusCode = code


class ExternalAPIRequest(BaseModel):
    endpoint: str
