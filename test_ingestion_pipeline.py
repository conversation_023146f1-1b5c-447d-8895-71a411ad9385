#!/usr/bin/env python3
"""
Test script for the complete ingestion pipeline.

This script tests the end-to-end ingestion process:
1. Parse the Cattlytics document using Docling
2. Chunk by headings with UI feature classification
3. Vectorize using OpenAI embeddings
4. Store in Qdrant Cloud vector database

Usage:
    python test_ingestion_pipeline.py
"""

import asyncio
import sys
import os
from pathlib import Path
from loguru import logger

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from bots.knowledge_base_bot.config import KnowledgeBaseConfig
from bots.knowledge_base_bot.ingestion.document_parser import DocumentParser
from bots.knowledge_base_bot.ingestion.document_chunker import DocumentChunker
from bots.knowledge_base_bot.ingestion.vectorizer import Vectorizer
from bots.knowledge_base_bot.ingestion.vector_store import VectorStore


async def test_ingestion_pipeline():
    """Test the complete ingestion pipeline."""
    try:
        logger.info("Starting ingestion pipeline test")
        
        # Initialize configuration with production credentials
        config = KnowledgeBaseConfig.from_production()
        logger.info("Configuration loaded successfully")
        
        # Initialize components
        parser = DocumentParser(config.parsing)
        chunker = DocumentChunker(config.chunking)
        vectorizer = Vectorizer(config.vectorization)
        vector_store = VectorStore(config.vector_store)
        
        logger.info("All components initialized")
        
        # Test document path
        document_path = "Cattlytics – Knowledge Base Scenarios.docx"
        if not os.path.exists(document_path):
            logger.error(f"Test document not found: {document_path}")
            return False
        
        logger.info(f"Testing with document: {document_path}")
        
        # Step 1: Parse document
        logger.info("Step 1: Parsing document with Docling...")
        parsed_document = await parser.parse(document_path)
        
        if not parsed_document:
            logger.error("Failed to parse document")
            return False
        
        logger.info(f"Document parsed successfully:")
        logger.info(f"  - Content length: {len(parsed_document.content)} characters")
        logger.info(f"  - Tables found: {len(parsed_document.tables) if parsed_document.tables else 0}")
        logger.info(f"  - Metadata: {parsed_document.metadata}")
        
        # Step 2: Chunk document by headings
        logger.info("Step 2: Chunking document by headings...")
        chunks = await chunker.chunk(parsed_document)
        
        if not chunks:
            logger.error("Failed to create chunks")
            return False
        
        logger.info(f"Document chunked successfully:")
        logger.info(f"  - Number of chunks: {len(chunks)}")
        
        # Display chunk information
        for i, chunk in enumerate(chunks):
            logger.info(f"  Chunk {i+1}:")
            logger.info(f"    - Heading: {chunk.heading_text} (Level {chunk.heading_level})")
            logger.info(f"    - Content length: {len(chunk.content)} characters")
            logger.info(f"    - UI features: {[f.value for f in chunk.ui_features]}")
            logger.info(f"    - Word count: {chunk.word_count}")
        
        # Step 3: Vectorize chunks
        logger.info("Step 3: Vectorizing chunks with OpenAI...")
        vectorized_chunks = await vectorizer.vectorize_chunks(chunks)
        
        if not vectorized_chunks:
            logger.error("Failed to vectorize chunks")
            return False
        
        logger.info(f"Chunks vectorized successfully:")
        logger.info(f"  - Number of vectorized chunks: {len(vectorized_chunks)}")
        logger.info(f"  - Embedding model: {vectorized_chunks[0].embedding_model}")
        logger.info(f"  - Vector dimension: {len(vectorized_chunks[0].vector)}")
        
        # Step 4: Store in Qdrant Cloud
        logger.info("Step 4: Storing vectors in Qdrant Cloud...")
        
        # Ensure collection exists
        await vector_store.initialize_collection()
        logger.info("Collection verified/created")
        
        # Store vectors
        success = await vector_store.index_chunks(vectorized_chunks)
        
        if not success:
            logger.error("Failed to store vectors")
            return False
        
        logger.info("Vectors stored successfully in Qdrant Cloud")
        
        # Step 5: Test search functionality
        logger.info("Step 5: Testing search functionality...")
        
        # Use the first chunk's vector for similarity search
        test_vector = vectorized_chunks[0].vector
        search_results = await vector_store.search(
            query_vector=test_vector,
            top_k=3,
            score_threshold=0.5
        )
        
        logger.info(f"Search test completed:")
        logger.info(f"  - Results found: {len(search_results)}")
        
        for i, result in enumerate(search_results):
            logger.info(f"  Result {i+1}:")
            logger.info(f"    - Score: {result['score']:.4f}")
            logger.info(f"    - Heading: {result.get('heading_text', 'N/A')}")
            logger.info(f"    - UI features: {result.get('ui_features', [])}")
            logger.info(f"    - Content preview: {result['content'][:100]}...")
        
        logger.success("🎉 Complete ingestion pipeline test PASSED!")
        logger.info("All components working correctly:")
        logger.info("  ✅ Document parsing with Docling")
        logger.info("  ✅ Heading-based chunking with UI feature classification")
        logger.info("  ✅ OpenAI vectorization")
        logger.info("  ✅ Qdrant Cloud storage")
        logger.info("  ✅ Vector similarity search")
        
        return True
        
    except Exception as e:
        logger.error(f"Pipeline test failed with error: {str(e)}")
        logger.exception("Full error details:")
        return False


async def main():
    """Main function to run the test."""
    logger.info("=" * 60)
    logger.info("CATTLYTICS KNOWLEDGE BASE - INGESTION PIPELINE TEST")
    logger.info("=" * 60)
    
    success = await test_ingestion_pipeline()
    
    if success:
        logger.info("=" * 60)
        logger.success("TEST COMPLETED SUCCESSFULLY! 🎉")
        logger.info("The ingestion pipeline is ready for production use.")
        logger.info("=" * 60)
        sys.exit(0)
    else:
        logger.error("=" * 60)
        logger.error("TEST FAILED! ❌")
        logger.error("Please check the errors above and fix the issues.")
        logger.error("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Run the test
    asyncio.run(main())
